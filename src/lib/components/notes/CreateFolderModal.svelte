<script lang="ts">
	import { createEventDispatcher, getContext } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { fade } from 'svelte/transition';
	import { flyAndScale } from '$lib/utils/transitions';
	import AccessControl from '$lib/components/workspace/common/AccessControl.svelte';
	import type { NoteFolderForm } from '$lib/apis/note-folders';
	import type { I18n } from '$lib/models/i18n';

	const i18n = getContext<I18n>('i18n');
	const dispatch = createEventDispatcher();

	export let show = false;
	export let parentId: string | null = null;

	let folderName = '';
	let folderDescription = '';
	let folderColor = '#3b82f6';
	let folderAccessControl = {};
	let modalElement: HTMLElement | null = null;

	const handleKeyDown = (event: KeyboardEvent) => {
		if (event.key === 'Escape') {
			cancelHandler();
		}
		if (event.key === 'Enter') {
			confirmHandler();
		}
	};

	const confirmHandler = () => {
		if (!folderName.trim()) {
			toast.error($i18n.t('Folder name is required'));
			return;
		}

		const folderData: NoteFolderForm = {
			name: folderName.trim(),
			description: folderDescription.trim() || undefined,
			color: folderColor,
			parent_id: parentId,
			sort_order: 0,
			access_control: folderAccessControl
		};

		dispatch('confirm', folderData);
		resetForm();
		show = false;
	};

	const cancelHandler = () => {
		resetForm();
		show = false;
		dispatch('cancel');
	};

	const resetForm = () => {
		folderName = '';
		folderDescription = '';
		folderColor = '#3b82f6';
		folderAccessControl = {};
	};

	$: if (show && modalElement) {
		document.body.appendChild(modalElement);
		window.addEventListener('keydown', handleKeyDown);
		document.body.style.overflow = 'hidden';
	} else if (modalElement) {
		window.removeEventListener('keydown', handleKeyDown);
		if (document.body.contains(modalElement)) {
			document.body.removeChild(modalElement);
		}
		document.body.style.overflow = 'unset';
	}

	// 當 show 變為 true 時重置表單
	$: if (show) {
		resetForm();
	}
</script>

{#if show}
	<!-- svelte-ignore a11y-click-events-have-key-events -->
	<!-- svelte-ignore a11y-no-static-element-interactions -->
	<div
		bind:this={modalElement}
		class="fixed top-0 right-0 left-0 bottom-0 bg-black/60 w-full h-screen max-h-[100dvh] flex justify-center z-99999999 overflow-hidden overscroll-contain"
		in:fade={{ duration: 10 }}
		on:mousedown={cancelHandler}
	>
		<div
			class="m-auto rounded-2xl max-w-full w-[32rem] mx-2 bg-white dark:bg-gray-900 max-h-[100dvh] shadow-3xl"
			in:flyAndScale
			on:mousedown={(e) => {
				e.stopPropagation();
			}}
		>
			<div class="px-[1.75rem] py-6 flex flex-col">
				<div class="text-lg font-semibold dark:text-white mb-2.5">
					{$i18n.t('Create Folder')}
				</div>

				<div class="space-y-4">
					<div>
						<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							{$i18n.t('Name')}
						</label>
						<input
							type="text"
							bind:value={folderName}
							class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
							placeholder={$i18n.t('Enter folder name')}
						/>
					</div>

					<div>
						<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							{$i18n.t('Description')} ({$i18n.t('Optional')})
						</label>
						<textarea
							bind:value={folderDescription}
							class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
							rows="2"
							placeholder={$i18n.t('Enter folder description')}
						></textarea>
					</div>

					<div>
						<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							{$i18n.t('Color')}
						</label>
						<input
							type="color"
							bind:value={folderColor}
							class="w-full h-10 border border-gray-300 dark:border-gray-600 rounded-md"
						/>
					</div>

					<div>
						<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
							{$i18n.t('Access Control')}
						</label>
						<div class="dark:text-white">
							<AccessControl
								bind:accessControl={folderAccessControl}
								accessRoles={['read', 'write']}
								allowPublic={true}
							/>
						</div>
					</div>
				</div>

				<div class="flex justify-between gap-1.5 mt-6">
					<button
						class="bg-gray-100 hover:bg-gray-200 text-gray-800 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white font-medium w-full py-2.5 rounded-lg transition"
						on:click={cancelHandler}
						type="button"
					>
						{$i18n.t('Cancel')}
					</button>
					<button
						class="bg-blue-600 hover:bg-blue-700 text-white font-medium w-full py-2.5 rounded-lg transition"
						on:click={confirmHandler}
						type="button"
						disabled={!folderName.trim()}
					>
						{$i18n.t('Create')}
					</button>
				</div>
			</div>
		</div>
	</div>
{/if}
